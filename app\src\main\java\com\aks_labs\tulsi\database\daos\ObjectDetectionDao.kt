package com.aks_labs.tulsi.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.aks_labs.tulsi.database.entities.ObjectDetectionEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for object detection operations
 */
@Dao
interface ObjectDetectionDao {
    
    /**
     * Insert object detection result
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertObjectDetection(objectDetection: ObjectDetectionEntity)
    
    /**
     * Insert multiple object detection results
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertObjectDetections(objectDetections: List<ObjectDetectionEntity>)
    
    /**
     * Get all object detections for a specific media item
     */
    @Query("SELECT * FROM object_detection WHERE media_id = :mediaId ORDER BY confidence_score DESC")
    suspend fun getObjectDetectionsByMediaId(mediaId: Long): List<ObjectDetectionEntity>
    
    /**
     * Get all media IDs that have a specific object detected
     */
    @Query("SELECT DISTINCT media_id FROM object_detection WHERE object_label LIKE '%' || :objectLabel || '%' AND confidence_score >= :minConfidence ORDER BY confidence_score DESC")
    suspend fun getMediaIdsByObjectLabel(objectLabel: String, minConfidence: Float = 0.5f): List<Long>
    
    /**
     * Search for media by object labels (supports multiple labels)
     */
    @Query("""
        SELECT DISTINCT media_id, MAX(confidence_score) as max_confidence 
        FROM object_detection 
        WHERE object_label IN (:objectLabels) 
        AND confidence_score >= :minConfidence 
        GROUP BY media_id 
        ORDER BY max_confidence DESC
    """)
    suspend fun searchMediaByObjectLabels(objectLabels: List<String>, minConfidence: Float = 0.5f): List<MediaIdWithConfidence>
    
    /**
     * Get all unique object labels detected across all media
     */
    @Query("SELECT DISTINCT object_label FROM object_detection WHERE confidence_score >= :minConfidence ORDER BY object_label ASC")
    suspend fun getAllDetectedObjectLabels(minConfidence: Float = 0.5f): List<String>
    
    /**
     * Get object detection statistics
     */
    @Query("""
        SELECT 
            COUNT(DISTINCT media_id) as total_processed_images,
            COUNT(*) as total_objects_detected,
            AVG(confidence_score) as average_confidence,
            COUNT(DISTINCT object_label) as unique_object_types
        FROM object_detection
    """)
    suspend fun getObjectDetectionStats(): ObjectDetectionStats?
    
    /**
     * Check if media item has been processed for object detection
     */
    @Query("SELECT COUNT(*) > 0 FROM object_detection WHERE media_id = :mediaId")
    suspend fun isMediaProcessed(mediaId: Long): Boolean
    
    /**
     * Get all processed media IDs
     */
    @Query("SELECT DISTINCT media_id FROM object_detection")
    suspend fun getAllProcessedMediaIds(): List<Long>
    
    /**
     * Delete object detection results for a specific media item
     */
    @Query("DELETE FROM object_detection WHERE media_id = :mediaId")
    suspend fun deleteObjectDetectionsByMediaId(mediaId: Long)
    
    /**
     * Get object detection results with confidence above threshold
     */
    @Query("SELECT * FROM object_detection WHERE confidence_score >= :minConfidence ORDER BY confidence_score DESC")
    suspend fun getHighConfidenceDetections(minConfidence: Float = 0.7f): List<ObjectDetectionEntity>
    
    /**
     * Get most common detected objects
     */
    @Query("""
        SELECT object_label, COUNT(*) as count, AVG(confidence_score) as avg_confidence
        FROM object_detection 
        WHERE confidence_score >= :minConfidence
        GROUP BY object_label 
        ORDER BY count DESC 
        LIMIT :limit
    """)
    suspend fun getMostCommonObjects(minConfidence: Float = 0.5f, limit: Int = 20): List<ObjectLabelStats>
    
    /**
     * Get total number of processed images (suspend version)
     */
    @Query("SELECT COUNT(DISTINCT media_id) FROM object_detection")
    suspend fun getTotalProcessedImages(): Int

    /**
     * Flow for real-time object detection updates
     */
    @Query("SELECT COUNT(DISTINCT media_id) FROM object_detection")
    fun getProcessedImageCountFlow(): Flow<Int>
}

/**
 * Data class for media ID with confidence score
 */
data class MediaIdWithConfidence(
    val media_id: Long,
    val max_confidence: Float
)

/**
 * Data class for object detection statistics
 */
data class ObjectDetectionStats(
    val total_processed_images: Int,
    val total_objects_detected: Int,
    val average_confidence: Float,
    val unique_object_types: Int
)

/**
 * Data class for object label statistics
 */
data class ObjectLabelStats(
    val object_label: String,
    val count: Int,
    val avg_confidence: Float
)
