package com.aks_labs.tulsi.models.search_page

import android.content.Context
import android.os.CancellationSignal
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.room.Room
import com.aks_labs.tulsi.database.MediaDatabase
import com.aks_labs.tulsi.database.Migration3to4
import com.aks_labs.tulsi.database.Migration4to5
import com.aks_labs.tulsi.database.Migration5to6
import com.aks_labs.tulsi.database.Migration6to7
import com.aks_labs.tulsi.database.Migration7to8
import com.aks_labs.tulsi.database.entities.SearchHistoryEntity
import com.aks_labs.tulsi.datastore.SQLiteQuery
import com.aks_labs.tulsi.helpers.MediaItemSortMode
import com.aks_labs.tulsi.mediastore.MediaStoreData
import com.aks_labs.tulsi.mediastore.MultiAlbumDataSource
import com.aks_labs.tulsi.objectdetection.ObjectDetectionManager
import com.aks_labs.tulsi.ocr.SimpleOcrService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class SearchViewModel(context: Context, sortBy: MediaItemSortMode) : ViewModel() {
	private val cancellationSignal = CancellationSignal()
    private val mediaStoreDataSource =
				    MultiAlbumDataSource(
				    	context = context,
				    	queryString = SQLiteQuery(query = "", paths = null),
				    	sortBy = sortBy,
				    	cancellationSignal = cancellationSignal
				    )

    // Database for object detection
    private val database by lazy {
        Room.databaseBuilder(
            context,
            MediaDatabase::class.java,
            "media-database"
        ).apply {
            addMigrations(
                Migration3to4(context),
                Migration4to5(context),
                Migration5to6(context),
                Migration6to7(context),
                Migration7to8(context)
            )
        }.build()
    }

    // Simple OCR service
    private val ocrService = SimpleOcrService.getInstance(context)

    // Object detection manager
    private val objectDetectionManager by lazy {
        ObjectDetectionManager(context, database)
    }

    val mediaFlow by lazy {
        getMediaDataFlow().stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    }

    // Search suggestions flow
    private val _searchSuggestions = MutableStateFlow<List<String>>(emptyList())
    val searchSuggestions: StateFlow<List<String>> = _searchSuggestions.asStateFlow()

    // Search history flow
    private val _searchHistory = MutableStateFlow<List<SearchHistoryEntity>>(emptyList())
    val searchHistory: StateFlow<List<SearchHistoryEntity>> = _searchHistory.asStateFlow()

    // OCR search results flow
    private val _ocrSearchResults = MutableStateFlow<List<MediaStoreData>>(emptyList())
    val ocrSearchResults: StateFlow<List<MediaStoreData>> = _ocrSearchResults.asStateFlow()

    // Object detection search results flow
    private val _objectDetectionSearchResults = MutableStateFlow<List<MediaStoreData>>(emptyList())
    val objectDetectionSearchResults: StateFlow<List<MediaStoreData>> = _objectDetectionSearchResults.asStateFlow()

    private fun getMediaDataFlow(): Flow<List<MediaStoreData>> = mediaStoreDataSource.loadMediaStoreData().flowOn(Dispatchers.IO)

    fun cancelMediaFlow() = cancellationSignal.cancel()

    /**
     * Search images by OCR text content
     */
    suspend fun searchByOcrText(query: String): List<Long> {
        if (query.isBlank()) {
            _ocrSearchResults.value = emptyList()
            return emptyList()
        }

        return try {
            val mediaIds = ocrService.searchImagesByText(query)

            // Filter current media items by OCR results
            val currentMedia = mediaFlow.value
            val filteredMedia = currentMedia.filter { mediaItem ->
                mediaIds.contains(mediaItem.id)
            }

            _ocrSearchResults.value = filteredMedia

            // Save search to history
            saveSearchToHistory(query, "ocr", filteredMedia.size)

            mediaIds
        } catch (e: Exception) {
            _ocrSearchResults.value = emptyList()
            emptyList()
        }
    }

    /**
     * Search images by object detection
     */
    suspend fun searchByObjectLabels(query: String): List<Long> {
        if (query.isBlank()) {
            _objectDetectionSearchResults.value = emptyList()
            return emptyList()
        }

        return try {
            // Parse query into user search terms (split by comma or space)
            val userTerms = query.trim()
                .split(Regex("[,\\s]+"))
                .filter { it.isNotBlank() }
                .map { it.lowercase().trim() }

            if (userTerms.isEmpty()) {
                _objectDetectionSearchResults.value = emptyList()
                return emptyList()
            }

            // Map user search terms to ML Kit's generic object detection labels
            val mlKitLabels = mapUserTermsToMLKitLabels(userTerms)

            // Check if we have any object detection data at all
            val totalProcessedImages = objectDetectionManager.getTotalProcessedImages()

            if (totalProcessedImages == 0) {
                // No object detection data exists, start processing
                Log.d("SearchViewModel", "No object detection data found, starting batch processing...")
                objectDetectionManager.processBatch(50) // Process first 50 images

                // Return empty results for now, user can search again after processing
                _objectDetectionSearchResults.value = emptyList()
                return emptyList()
            }

            // Debug: Show what labels we're searching for
            Log.d("SearchViewModel", "User search terms: $userTerms")
            Log.d("SearchViewModel", "Mapped to ML Kit labels: $mlKitLabels")

            // Debug: Show what labels exist in database
            val allLabels = objectDetectionManager.getAllDetectedObjectLabels()
            Log.d("SearchViewModel", "Available object labels in database: $allLabels")

            val mediaIds = objectDetectionManager.searchByObjectLabels(mlKitLabels)

            Log.d("SearchViewModel", "Object detection search for '$query' found ${mediaIds.size} results")

            // Filter current media items by object detection results
            val currentMedia = mediaFlow.value
            val filteredMedia = currentMedia.filter { mediaItem ->
                mediaIds.contains(mediaItem.id)
            }

            _objectDetectionSearchResults.value = filteredMedia

            // Save search to history
            saveSearchToHistory(query, "object_detection", filteredMedia.size)

            mediaIds
        } catch (e: Exception) {
            Log.e("SearchViewModel", "Object detection search failed", e)
            _objectDetectionSearchResults.value = emptyList()
            emptyList()
        }
    }

    /**
     * Get search suggestions based on input
     */
    fun getSearchSuggestions(input: String) {
        if (input.length < 2) {
            _searchSuggestions.value = emptyList()
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // For now, we'll skip search suggestions since we removed the database dependency
                // You can implement this later if needed
                _searchSuggestions.value = emptyList()
            } catch (e: Exception) {
                _searchSuggestions.value = emptyList()
            }
        }
    }

    /**
     * Load recent search history
     */
    fun loadSearchHistory() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // For now, we'll skip search history since we simplified the approach
                // You can implement this later if needed
                _searchHistory.value = emptyList()
            } catch (e: Exception) {
                _searchHistory.value = emptyList()
            }
        }
    }

    /**
     * Save search query to history
     */
    private suspend fun saveSearchToHistory(query: String, type: String, resultsCount: Int) {
        try {
            // For now, we'll skip saving search history since we simplified the approach
            // You can implement this later if needed
        } catch (e: Exception) {
            // Ignore history save errors
        }
    }

    /**
     * Clear search history
     */
    fun clearSearchHistory() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // For now, we'll skip clearing search history since we simplified the approach
                _searchHistory.value = emptyList()
            } catch (e: Exception) {
                // Ignore clear errors
            }
        }
    }

    /**
     * Start OCR processing for an image
     */
    fun processImageForOcr(mediaItem: MediaStoreData) {
        ocrService.processImage(mediaItem.id, mediaItem.uri)
    }

    /**
     * Start batch OCR processing
     */
    fun startBatchOcrProcessing() {
        ocrService.startOcrProcessing()
    }

    /**
     * Get OCR processing statistics
     */
    fun getOcrStats() = viewModelScope.launch(Dispatchers.IO) {
        try {
            val stats = ocrService.getProcessingStats()
            // You can expose this via StateFlow if needed
        } catch (e: Exception) {
            // Handle error
        }
    }

    /**
     * Start object detection processing for an image
     */
    fun processImageForObjectDetection(mediaItem: MediaStoreData) {
        objectDetectionManager.processImageInBackground(mediaItem)
    }

    /**
     * Start batch object detection processing
     */
    fun startBatchObjectDetectionProcessing() {
        objectDetectionManager.processBatch()
    }

    /**
     * Get object detection processing statistics
     */
    fun getObjectDetectionStats() = viewModelScope.launch(Dispatchers.IO) {
        try {
            val stats = objectDetectionManager.getObjectDetectionStats()
            // You can expose this via StateFlow if needed
        } catch (e: Exception) {
            // Handle error
        }
    }

    /**
     * Get all detected object labels for suggestions
     */
    suspend fun getAllDetectedObjectLabels(): List<String> {
        return try {
            objectDetectionManager.getAllDetectedObjectLabels()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Map user search terms to ML Kit's generic object detection labels
     * ML Kit Object Detection only returns generic categories, not specific objects
     */
    private fun mapUserTermsToMLKitLabels(userTerms: List<String>): List<String> {
        val mlKitLabels = mutableSetOf<String>()

        // ML Kit Object Detection label mapping
        val labelMappings = mapOf(
            // Food-related terms
            "food" to listOf("Food"),
            "eat" to listOf("Food"),
            "meal" to listOf("Food"),
            "snack" to listOf("Food"),
            "drink" to listOf("Food"),
            "beverage" to listOf("Food"),
            "fruit" to listOf("Food"),
            "vegetable" to listOf("Food"),
            "pizza" to listOf("Food"),
            "burger" to listOf("Food"),
            "sandwich" to listOf("Food"),
            "cake" to listOf("Food"),
            "bread" to listOf("Food"),
            "coffee" to listOf("Food"),
            "tea" to listOf("Food"),
            "wine" to listOf("Food"),
            "beer" to listOf("Food"),

            // Plant-related terms
            "plant" to listOf("Plant"),
            "flower" to listOf("Plant"),
            "tree" to listOf("Plant"),
            "garden" to listOf("Plant"),
            "leaf" to listOf("Plant"),
            "grass" to listOf("Plant"),
            "nature" to listOf("Plant"),
            "outdoor" to listOf("Plant"),

            // Fashion-related terms
            "fashion" to listOf("Fashion good"),
            "clothing" to listOf("Fashion good"),
            "clothes" to listOf("Fashion good"),
            "shirt" to listOf("Fashion good"),
            "dress" to listOf("Fashion good"),
            "pants" to listOf("Fashion good"),
            "shoes" to listOf("Fashion good"),
            "bag" to listOf("Fashion good"),
            "purse" to listOf("Fashion good"),
            "hat" to listOf("Fashion good"),
            "jacket" to listOf("Fashion good"),
            "coat" to listOf("Fashion good"),
            "jewelry" to listOf("Fashion good"),
            "watch" to listOf("Fashion good"),
            "sunglasses" to listOf("Fashion good"),

            // Home/furniture-related terms
            "home" to listOf("Home good"),
            "furniture" to listOf("Home good"),
            "chair" to listOf("Home good"),
            "table" to listOf("Home good"),
            "bed" to listOf("Home good"),
            "sofa" to listOf("Home good"),
            "couch" to listOf("Home good"),
            "lamp" to listOf("Home good"),
            "kitchen" to listOf("Home good"),
            "bathroom" to listOf("Home good"),
            "bedroom" to listOf("Home good"),
            "living" to listOf("Home good"),
            "room" to listOf("Home good"),
            "house" to listOf("Home good"),
            "indoor" to listOf("Home good"),

            // Sports-related terms
            "sport" to listOf("Sporting good"),
            "sports" to listOf("Sporting good"),
            "ball" to listOf("Sporting good"),
            "game" to listOf("Sporting good"),
            "exercise" to listOf("Sporting good"),
            "fitness" to listOf("Sporting good"),
            "gym" to listOf("Sporting good"),
            "bike" to listOf("Sporting good"),
            "bicycle" to listOf("Sporting good"),
            "tennis" to listOf("Sporting good"),
            "football" to listOf("Sporting good"),
            "basketball" to listOf("Sporting good"),
            "soccer" to listOf("Sporting good"),
            "baseball" to listOf("Sporting good"),
            "golf" to listOf("Sporting good"),
            "swimming" to listOf("Sporting good"),
            "running" to listOf("Sporting good"),

            // Vehicle-related terms (may map to multiple categories)
            "car" to listOf("Vehicle"),
            "vehicle" to listOf("Vehicle"),
            "truck" to listOf("Vehicle"),
            "bus" to listOf("Vehicle"),
            "motorcycle" to listOf("Vehicle"),
            "bike" to listOf("Vehicle", "Sporting good"),
            "transport" to listOf("Vehicle"),
            "travel" to listOf("Vehicle"),

            // Animal-related terms
            "animal" to listOf("Animal"),
            "pet" to listOf("Animal"),
            "dog" to listOf("Animal"),
            "cat" to listOf("Animal"),
            "bird" to listOf("Animal"),
            "fish" to listOf("Animal"),
            "horse" to listOf("Animal"),
            "cow" to listOf("Animal"),
            "sheep" to listOf("Animal"),
            "pig" to listOf("Animal"),
            "chicken" to listOf("Animal"),
            "duck" to listOf("Animal"),
            "rabbit" to listOf("Animal"),
            "mouse" to listOf("Animal"),
            "elephant" to listOf("Animal"),
            "lion" to listOf("Animal"),
            "tiger" to listOf("Animal"),
            "bear" to listOf("Animal"),
            "deer" to listOf("Animal"),
            "zoo" to listOf("Animal"),

            // Person-related terms
            "person" to listOf("Person"),
            "people" to listOf("Person"),
            "human" to listOf("Person"),
            "man" to listOf("Person"),
            "woman" to listOf("Person"),
            "child" to listOf("Person"),
            "baby" to listOf("Person"),
            "family" to listOf("Person"),
            "friend" to listOf("Person"),
            "group" to listOf("Person"),
            "crowd" to listOf("Person"),
            "face" to listOf("Person"),
            "portrait" to listOf("Person"),
            "selfie" to listOf("Person")
        )

        // Map each user term to ML Kit labels
        for (userTerm in userTerms) {
            val mappedLabels = labelMappings[userTerm.lowercase()]
            if (mappedLabels != null) {
                mlKitLabels.addAll(mappedLabels)
            } else {
                // If no mapping found, try the original term (in case it's already an ML Kit label)
                mlKitLabels.add(userTerm)
            }
        }

        return mlKitLabels.toList()
    }

    /**
     * Clean up resources
     */
    override fun onCleared() {
        super.onCleared()
        objectDetectionManager.cleanup()
    }
}


