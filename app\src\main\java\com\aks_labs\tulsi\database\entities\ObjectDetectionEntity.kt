package com.aks_labs.tulsi.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Entity for storing object detection results from ML Kit
 * Similar to OcrTextEntity but for detected objects
 */
@Entity(
    tableName = "object_detection",
    foreignKeys = [
        ForeignKey(
            entity = MediaEntity::class,
            parentColumns = ["id"],
            childColumns = ["media_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["media_id"]),
        Index(value = ["object_label"]),
        Index(value = ["confidence_score"]),
        Index(value = ["detection_timestamp"])
    ]
)
data class ObjectDetectionEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "media_id")
    val mediaId: Long,
    
    @ColumnInfo(name = "object_label")
    val objectLabel: String,
    
    @ColumnInfo(name = "confidence_score")
    val confidenceScore: Float,
    
    @ColumnInfo(name = "detection_timestamp")
    val detectionTimestamp: Long,
    
    @ColumnInfo(name = "bounding_box_left")
    val boundingBoxLeft: Float? = null,
    
    @ColumnInfo(name = "bounding_box_top")
    val boundingBoxTop: Float? = null,
    
    @ColumnInfo(name = "bounding_box_right")
    val boundingBoxRight: Float? = null,
    
    @ColumnInfo(name = "bounding_box_bottom")
    val boundingBoxBottom: Float? = null,
    
    @ColumnInfo(name = "processing_time_ms")
    val processingTimeMs: Long = 0
)

/**
 * Data class for object detection results
 */
data class DetectedObject(
    val label: String,
    val confidence: Float,
    val boundingBox: BoundingBox? = null
)

/**
 * Data class for bounding box coordinates
 */
data class BoundingBox(
    val left: Float,
    val top: Float,
    val right: Float,
    val bottom: Float
)

/**
 * Sealed class for object detection results
 */
sealed class ObjectDetectionResult {
    data class Success(
        val detectedObjects: List<DetectedObject>,
        val processingTimeMs: Long
    ) : ObjectDetectionResult()
    
    data class Error(val message: String) : ObjectDetectionResult()
}
