package com.aks_labs.tulsi.objectdetection

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import androidx.room.Room
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.aks_labs.tulsi.database.MediaDatabase
import com.aks_labs.tulsi.database.Migration3to4
import com.aks_labs.tulsi.database.Migration4to5
import com.aks_labs.tulsi.database.Migration5to6
import com.aks_labs.tulsi.database.Migration6to7
import com.aks_labs.tulsi.database.Migration7to8
import com.aks_labs.tulsi.mediastore.MediaStoreData
import com.aks_labs.tulsi.mediastore.MediaType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Background worker for object detection processing
 * Similar to OcrIndexingWorker but for object detection
 */
class ObjectDetectionWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "ObjectDetectionWorker"
        private const val KEY_TOTAL_PROCESSED = "total_processed"
        private const val KEY_PROGRESS = "progress"
        private const val DEFAULT_BATCH_SIZE = 10
    }
    
    private val database by lazy {
        Room.databaseBuilder(
            applicationContext,
            MediaDatabase::class.java,
            "media-database"
        ).apply {
            addMigrations(
                Migration3to4(applicationContext),
                Migration4to5(applicationContext),
                Migration5to6(applicationContext),
                Migration6to7(applicationContext),
                Migration7to8(applicationContext)
            )
        }.build()
    }
    
    private val objectDetectionManager by lazy {
        ObjectDetectionManager(applicationContext, database)
    }
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        return@withContext try {
            val mediaId = inputData.getLong("media_id", -1L)
            val mediaUri = inputData.getString("media_uri")
            val batchSize = inputData.getInt("batch_size", DEFAULT_BATCH_SIZE)
            
            if (mediaId != -1L && mediaUri != null) {
                // Process single image
                processSingleImage(mediaId, Uri.parse(mediaUri))
            } else {
                // Process batch of images
                processBatchImages(batchSize)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Object detection worker failed", e)
            Result.failure(workDataOf(KEY_PROGRESS to "Failed: ${e.message}"))
        }
    }
    
    /**
     * Process a single image for object detection
     */
    private suspend fun processSingleImage(mediaId: Long, mediaUri: Uri): Result {
        return try {
            Log.d(TAG, "Processing single image for object detection: $mediaId")
            
            // Check if already processed
            if (database.objectDetectionDao().isMediaProcessed(mediaId)) {
                Log.d(TAG, "Image $mediaId already processed for object detection")
                return Result.success(workDataOf(
                    KEY_TOTAL_PROCESSED to 0,
                    KEY_PROGRESS to "Already processed"
                ))
            }
            
            // Create MediaStoreData object
            val mediaStoreData = MediaStoreData(
                type = MediaType.Image,
                id = mediaId,
                uri = mediaUri,
                mimeType = "image/*",
                dateModified = System.currentTimeMillis() / 1000,
                dateTaken = System.currentTimeMillis() / 1000,
                displayName = "Image_$mediaId",
                absolutePath = mediaUri.toString()
            )
            
            // Process the image
            val success = objectDetectionManager.processImage(mediaStoreData)
            
            if (success) {
                Log.d(TAG, "Successfully processed image $mediaId for object detection")
                Result.success(workDataOf(
                    KEY_TOTAL_PROCESSED to 1,
                    KEY_PROGRESS to "Processed image $mediaId"
                ))
            } else {
                Log.w(TAG, "Failed to process image $mediaId for object detection")
                Result.failure(workDataOf(KEY_PROGRESS to "Failed to process image $mediaId"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Exception processing single image $mediaId", e)
            Result.failure(workDataOf(KEY_PROGRESS to "Exception: ${e.message}"))
        }
    }
    
    /**
     * Process batch of images for object detection
     */
    private suspend fun processBatchImages(batchSize: Int): Result {
        return try {
            Log.d(TAG, "Starting batch object detection processing with batch size: $batchSize")
            
            // Get all processed media IDs
            val processedIds = database.objectDetectionDao().getAllProcessedMediaIds().toSet()
            Log.d(TAG, "Found ${processedIds.size} already processed images")
            
            // Get unprocessed images from MediaStore
            val unprocessedImages = getAllImagesFromMediaStore().filter { imageInfo ->
                !processedIds.contains(imageInfo.id)
            }.take(batchSize)
            
            if (unprocessedImages.isEmpty()) {
                Log.d(TAG, "No unprocessed images found for object detection")
                return Result.success(workDataOf(
                    KEY_TOTAL_PROCESSED to 0,
                    KEY_PROGRESS to "No unprocessed images"
                ))
            }
            
            Log.d(TAG, "Processing ${unprocessedImages.size} unprocessed images for object detection")
            
            var processedCount = 0
            
            for ((index, imageInfo) in unprocessedImages.withIndex()) {
                try {
                    Log.d(TAG, "Processing image ${index + 1}/${unprocessedImages.size}: ${imageInfo.id}")
                    
                    // Create MediaStoreData object
                    val mediaStoreData = MediaStoreData(
                        type = MediaType.Image,
                        id = imageInfo.id,
                        uri = imageInfo.uri,
                        mimeType = "image/*",
                        dateModified = System.currentTimeMillis() / 1000,
                        dateTaken = System.currentTimeMillis() / 1000,
                        displayName = imageInfo.name,
                        absolutePath = imageInfo.uri.toString()
                    )
                    
                    // Process the image
                    val success = objectDetectionManager.processImage(mediaStoreData)
                    
                    if (success) {
                        processedCount++
                        Log.d(TAG, "Successfully processed image ${imageInfo.id} for object detection")
                    } else {
                        Log.w(TAG, "Failed to process image ${imageInfo.id} for object detection")
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Exception processing image ${imageInfo.id}", e)
                }
            }
            
            Log.d(TAG, "Batch object detection completed. Processed: $processedCount/${unprocessedImages.size}")
            
            Result.success(workDataOf(
                KEY_TOTAL_PROCESSED to processedCount,
                KEY_PROGRESS to "Processed $processedCount images"
            ))
            
        } catch (e: Exception) {
            Log.e(TAG, "Exception in batch object detection processing", e)
            Result.failure(workDataOf(KEY_PROGRESS to "Batch processing failed: ${e.message}"))
        }
    }
    
    /**
     * Get all images from MediaStore
     */
    private fun getAllImagesFromMediaStore(): List<ImageInfo> {
        val images = mutableListOf<ImageInfo>()
        
        try {
            val cursor = applicationContext.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                arrayOf(
                    MediaStore.Images.Media._ID,
                    MediaStore.Images.Media.DISPLAY_NAME
                ),
                null,
                null,
                "${MediaStore.Images.Media.DATE_ADDED} DESC"
            )
            
            cursor?.use {
                val idColumn = it.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val nameColumn = it.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
                
                while (it.moveToNext()) {
                    val id = it.getLong(idColumn)
                    val name = it.getString(nameColumn)
                    val uri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id.toString())
                    
                    images.add(ImageInfo(id, uri, name))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get images from MediaStore", e)
        }
        
        return images
    }
    
    /**
     * Data class for image information
     */
    data class ImageInfo(
        val id: Long,
        val uri: Uri,
        val name: String
    )
}
