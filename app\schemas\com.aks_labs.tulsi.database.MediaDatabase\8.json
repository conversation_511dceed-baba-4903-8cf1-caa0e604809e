{"formatVersion": 1, "database": {"version": 8, "identityHash": "36963f26cb3b8f10bf54c83d6def542b", "entities": [{"tableName": "MediaEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `date_taken` INTEGER NOT NULL, `mime_type` TEXT NOT NULL, `display_name` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dateTaken", "columnName": "date_taken", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeType", "columnName": "mime_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "display_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "TrashedItemEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`originalPath` TEXT NOT NULL, `trashed_path` TEXT NOT NULL, `date_taken` INTEGER NOT NULL, `mime_type` TEXT NOT NULL, `display_name` TEXT NOT NULL, PRIMARY KEY(`originalPath`))", "fields": [{"fieldPath": "originalPath", "columnName": "originalPath", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trashedPath", "columnName": "trashed_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "dateTaken", "columnName": "date_taken", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeType", "columnName": "mime_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "display_name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["originalPath"]}}, {"tableName": "FavouritedItemEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `date_taken` INTEGER NOT NULL, `mime_type` TEXT NOT NULL, `display_name` TEXT NOT NULL, `absolute_path` TEXT NOT NULL, `type` TEXT NOT NULL, `date_modified` INTEGER NOT NULL, `uri` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dateTaken", "columnName": "date_taken", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeType", "columnName": "mime_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "display_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "absolutePath", "columnName": "absolute_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "dateModified", "columnName": "date_modified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "SecuredItemEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`originalPath` TEXT NOT NULL, `secured_path` TEXT NOT NULL, `iv` BLOB NOT NULL, PRIMARY KEY(`originalPath`))", "fields": [{"fieldPath": "originalPath", "columnName": "originalPath", "affinity": "TEXT", "notNull": true}, {"fieldPath": "secured<PERSON><PERSON>", "columnName": "secured_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "iv", "columnName": "iv", "affinity": "BLOB", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["originalPath"]}}, {"tableName": "ocr_text", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `media_id` INTEGER NOT NULL, `extracted_text` TEXT NOT NULL, `extraction_timestamp` INTEGER NOT NULL, `confidence_score` REAL NOT NULL, `text_blocks_count` INTEGER NOT NULL, `processing_time_ms` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaId", "columnName": "media_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extractedText", "columnName": "extracted_text", "affinity": "TEXT", "notNull": true}, {"fieldPath": "extractionTimestamp", "columnName": "extraction_timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "confidenceScore", "columnName": "confidence_score", "affinity": "REAL", "notNull": true}, {"fieldPath": "textBlocksCount", "columnName": "text_blocks_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "processingTimeMs", "columnName": "processing_time_ms", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_ocr_text_media_id", "unique": true, "columnNames": ["media_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_ocr_text_media_id` ON `${TABLE_NAME}` (`media_id`)"}, {"name": "index_ocr_text_extracted_text", "unique": false, "columnNames": ["extracted_text"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_ocr_text_extracted_text` ON `${TABLE_NAME}` (`extracted_text`)"}, {"name": "index_ocr_text_extraction_timestamp", "unique": false, "columnNames": ["extraction_timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_ocr_text_extraction_timestamp` ON `${TABLE_NAME}` (`extraction_timestamp`)"}]}, {"tableName": "ocr_progress", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `total_images` INTEGER NOT NULL, `processed_images` INTEGER NOT NULL, `failed_images` INTEGER NOT NULL, `is_processing` INTEGER NOT NULL, `is_paused` INTEGER NOT NULL, `last_updated` INTEGER NOT NULL, `estimated_completion_time` INTEGER NOT NULL, `average_processing_time_ms` INTEGER NOT NULL, `current_batch_id` TEXT, `progress_dismissed` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalImages", "columnName": "total_images", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "processedImages", "columnName": "processed_images", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "failedImages", "columnName": "failed_images", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isProcessing", "columnName": "is_processing", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPaused", "columnName": "is_paused", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "last_updated", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "estimatedCompletionTime", "columnName": "estimated_completion_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageProcessingTimeMs", "columnName": "average_processing_time_ms", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentBatchId", "columnName": "current_batch_id", "affinity": "TEXT"}, {"fieldPath": "progressDismissed", "columnName": "progress_dismissed", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "object_detection", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `media_id` INTEGER NOT NULL, `object_label` TEXT NOT NULL, `confidence_score` REAL NOT NULL, `detection_timestamp` INTEGER NOT NULL, `bounding_box_left` REAL, `bounding_box_top` REAL, `bounding_box_right` REAL, `bounding_box_bottom` REAL, `processing_time_ms` INTEGER NOT NULL, FOREIGN KEY(`media_id`) REFERENCES `MediaEntity`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mediaId", "columnName": "media_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "objectLabel", "columnName": "object_label", "affinity": "TEXT", "notNull": true}, {"fieldPath": "confidenceScore", "columnName": "confidence_score", "affinity": "REAL", "notNull": true}, {"fieldPath": "detectionTimestamp", "columnName": "detection_timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "boundingBoxLeft", "columnName": "bounding_box_left", "affinity": "REAL"}, {"fieldPath": "boundingBoxTop", "columnName": "bounding_box_top", "affinity": "REAL"}, {"fieldPath": "boundingBoxRight", "columnName": "bounding_box_right", "affinity": "REAL"}, {"fieldPath": "boundingBoxBottom", "columnName": "bounding_box_bottom", "affinity": "REAL"}, {"fieldPath": "processingTimeMs", "columnName": "processing_time_ms", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_object_detection_media_id", "unique": false, "columnNames": ["media_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_object_detection_media_id` ON `${TABLE_NAME}` (`media_id`)"}, {"name": "index_object_detection_object_label", "unique": false, "columnNames": ["object_label"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_object_detection_object_label` ON `${TABLE_NAME}` (`object_label`)"}, {"name": "index_object_detection_confidence_score", "unique": false, "columnNames": ["confidence_score"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_object_detection_confidence_score` ON `${TABLE_NAME}` (`confidence_score`)"}, {"name": "index_object_detection_detection_timestamp", "unique": false, "columnNames": ["detection_timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_object_detection_detection_timestamp` ON `${TABLE_NAME}` (`detection_timestamp`)"}], "foreignKeys": [{"table": "MediaEntity", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["media_id"], "referencedColumns": ["id"]}]}, {"tableName": "search_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `search_query` TEXT NOT NULL, `search_timestamp` INTEGER NOT NULL, `search_type` TEXT NOT NULL, `results_count` INTEGER NOT NULL, `frequency_count` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "searchQuery", "columnName": "search_query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "searchTimestamp", "columnName": "search_timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "searchType", "columnName": "search_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "resultsCount", "columnName": "results_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "frequencyCount", "columnName": "frequency_count", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_search_history_search_query", "unique": false, "columnNames": ["search_query"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_search_history_search_query` ON `${TABLE_NAME}` (`search_query`)"}, {"name": "index_search_history_search_timestamp", "unique": false, "columnNames": ["search_timestamp"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_search_history_search_timestamp` ON `${TABLE_NAME}` (`search_timestamp`)"}, {"name": "index_search_history_search_type", "unique": false, "columnNames": ["search_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_search_history_search_type` ON `${TABLE_NAME}` (`search_type`)"}]}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '36963f26cb3b8f10bf54c83d6def542b')"]}}