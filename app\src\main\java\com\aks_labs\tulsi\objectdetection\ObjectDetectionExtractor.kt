package com.aks_labs.tulsi.objectdetection

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.objects.ObjectDetection
import com.google.mlkit.vision.objects.defaults.ObjectDetectorOptions
import com.aks_labs.tulsi.database.entities.BoundingBox
import com.aks_labs.tulsi.database.entities.DetectedObject
import com.aks_labs.tulsi.database.entities.ObjectDetectionResult
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * Service class for detecting objects in images using ML Kit Object Detection
 * Similar to OcrTextExtractor but for object detection
 */
class ObjectDetectionExtractor(private val context: Context) {
    
    companion object {
        private const val TAG = "ObjectDetectionExtractor"
        private const val MAX_IMAGE_SIZE = 1024 // Max dimension for object detection processing
        private const val OBJECT_DETECTION_TIMEOUT_MS = 5000L // 5 seconds timeout
        private const val MIN_CONFIDENCE_THRESHOLD = 0.5f // Minimum confidence to consider detection valid
    }
    
    // Configure object detector with optimized settings
    private val objectDetectorOptions = ObjectDetectorOptions.Builder()
        .setDetectorMode(ObjectDetectorOptions.SINGLE_IMAGE_MODE)
        .enableMultipleObjects()
        .enableClassification() // Enable classification to get object labels
        .build()
    
    private val objectDetector = ObjectDetection.getClient(objectDetectorOptions)
    
    /**
     * Extract objects from image URI
     */
    suspend fun extractObjectsFromImage(imageUri: Uri): ObjectDetectionResult {
        return try {
            val startTime = System.currentTimeMillis()
            Log.d(TAG, "Starting object detection for image: $imageUri")
            
            // Load and optimize bitmap
            val bitmap = loadAndOptimizeBitmap(imageUri)
                ?: return ObjectDetectionResult.Error("Failed to load image from URI: $imageUri")
            
            if (bitmap.width <= 0 || bitmap.height <= 0) {
                return ObjectDetectionResult.Error("Invalid bitmap dimensions: ${bitmap.width}x${bitmap.height}")
            }
            
            // Create InputImage for ML Kit
            val inputImage = try {
                InputImage.fromBitmap(bitmap, 0)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create InputImage from bitmap", e)
                if (!bitmap.isRecycled) bitmap.recycle()
                return ObjectDetectionResult.Error("Failed to create input image: ${e.message}")
            }
            
            // Perform object detection with timeout
            val detectedObjects = try {
                performObjectDetectionWithTimeout(inputImage)
            } catch (e: Exception) {
                Log.e(TAG, "Object detection processing failed", e)
                if (!bitmap.isRecycled) bitmap.recycle()
                return ObjectDetectionResult.Error("Object detection failed: ${e.message}")
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            
            // Clean up bitmap
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
            
            Log.d(TAG, "Object detection completed successfully in ${processingTime}ms for image: $imageUri")
            Log.d(TAG, "Detected ${detectedObjects.size} objects")
            
            ObjectDetectionResult.Success(
                detectedObjects = detectedObjects,
                processingTimeMs = processingTime
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Object detection failed for image: $imageUri", e)
            ObjectDetectionResult.Error("Object detection processing failed: ${e.message}")
        }
    }
    
    /**
     * Extract objects from bitmap directly
     */
    suspend fun extractObjectsFromBitmap(bitmap: Bitmap): ObjectDetectionResult {
        return try {
            val startTime = System.currentTimeMillis()
            Log.d(TAG, "Starting object detection for bitmap: ${bitmap.width}x${bitmap.height}")
            
            if (bitmap.width <= 0 || bitmap.height <= 0) {
                return ObjectDetectionResult.Error("Invalid bitmap dimensions: ${bitmap.width}x${bitmap.height}")
            }
            
            // Create InputImage for ML Kit
            val inputImage = try {
                InputImage.fromBitmap(bitmap, 0)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create InputImage from bitmap", e)
                return ObjectDetectionResult.Error("Failed to create input image: ${e.message}")
            }
            
            // Perform object detection with timeout
            val detectedObjects = try {
                performObjectDetectionWithTimeout(inputImage)
            } catch (e: Exception) {
                Log.e(TAG, "Object detection processing failed", e)
                return ObjectDetectionResult.Error("Object detection failed: ${e.message}")
            }
            
            val processingTime = System.currentTimeMillis() - startTime
            
            Log.d(TAG, "Object detection completed successfully in ${processingTime}ms for bitmap")
            Log.d(TAG, "Detected ${detectedObjects.size} objects")
            
            ObjectDetectionResult.Success(
                detectedObjects = detectedObjects,
                processingTimeMs = processingTime
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Object detection failed for bitmap", e)
            ObjectDetectionResult.Error("Object detection processing failed: ${e.message}")
        }
    }
    
    /**
     * Perform object detection with timeout
     */
    private suspend fun performObjectDetectionWithTimeout(inputImage: InputImage): List<DetectedObject> = 
        withTimeoutOrNull(OBJECT_DETECTION_TIMEOUT_MS) {
            performObjectDetection(inputImage)
        } ?: throw Exception("Object detection timed out after ${OBJECT_DETECTION_TIMEOUT_MS}ms")
    
    /**
     * Perform object detection using ML Kit
     */
    private suspend fun performObjectDetection(inputImage: InputImage): List<DetectedObject> = 
        suspendCancellableCoroutine { continuation ->
            objectDetector.process(inputImage)
                .addOnSuccessListener { detectedObjects ->
                    val results = detectedObjects.mapNotNull { detectedObject ->
                        // Get the best label (highest confidence)
                        val bestLabel = detectedObject.labels.maxByOrNull { it.confidence }
                        
                        if (bestLabel != null && bestLabel.confidence >= MIN_CONFIDENCE_THRESHOLD) {
                            DetectedObject(
                                label = bestLabel.text,
                                confidence = bestLabel.confidence,
                                boundingBox = detectedObject.boundingBox?.let { rect ->
                                    BoundingBox(
                                        left = rect.left.toFloat(),
                                        top = rect.top.toFloat(),
                                        right = rect.right.toFloat(),
                                        bottom = rect.bottom.toFloat()
                                    )
                                }
                            )
                        } else null
                    }
                    continuation.resume(results)
                }
                .addOnFailureListener { exception ->
                    continuation.resumeWithException(exception)
                }
        }
    
    /**
     * Load and optimize bitmap from URI
     */
    private fun loadAndOptimizeBitmap(imageUri: Uri): Bitmap? {
        return try {
            val inputStream = context.contentResolver.openInputStream(imageUri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (originalBitmap == null) {
                Log.e(TAG, "Failed to decode bitmap from URI: $imageUri")
                return null
            }
            
            // Optimize bitmap size for better performance
            optimizeBitmapSize(originalBitmap)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load bitmap from URI: $imageUri", e)
            null
        }
    }
    
    /**
     * Optimize bitmap size for object detection processing
     */
    private fun optimizeBitmapSize(bitmap: Bitmap): Bitmap {
        val maxDimension = maxOf(bitmap.width, bitmap.height)
        
        return if (maxDimension > MAX_IMAGE_SIZE) {
            val scaleFactor = MAX_IMAGE_SIZE.toFloat() / maxDimension
            val newWidth = (bitmap.width * scaleFactor).toInt()
            val newHeight = (bitmap.height * scaleFactor).toInt()
            
            Log.d(TAG, "Scaling bitmap from ${bitmap.width}x${bitmap.height} to ${newWidth}x${newHeight}")
            
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            if (scaledBitmap != bitmap) {
                bitmap.recycle()
            }
            scaledBitmap
        } else {
            bitmap
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            objectDetector.close()
        } catch (e: Exception) {
            Log.w(TAG, "Failed to close object detector", e)
        }
    }
}
