package com.aks_labs.tulsi.objectdetection

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.aks_labs.tulsi.database.MediaDatabase
import com.aks_labs.tulsi.database.entities.ObjectDetectionEntity
import com.aks_labs.tulsi.database.entities.ObjectDetectionResult
import com.aks_labs.tulsi.mediastore.MediaStoreData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * Manager class for coordinating object detection operations
 * Similar to OcrManager but for object detection
 */
class ObjectDetectionManager(
    private val context: Context,
    private val database: MediaDatabase
) {
    
    companion object {
        private const val TAG = "ObjectDetectionManager"
        private const val WORK_NAME_BATCH_OBJECT_DETECTION = "batch_object_detection"
        private const val WORK_NAME_SINGLE_OBJECT_DETECTION = "single_object_detection_"
    }
    
    private val workManager = WorkManager.getInstance(context)
    private val objectDetectionExtractor = ObjectDetectionExtractor(context)
    
    /**
     * Process a single image for object detection
     */
    suspend fun processImage(mediaItem: MediaStoreData): Boolean {
        return try {
            Log.d(TAG, "Processing image for object detection: ${mediaItem.id}")
            
            // Check if already processed
            if (database.objectDetectionDao().isMediaProcessed(mediaItem.id)) {
                Log.d(TAG, "Image ${mediaItem.id} already processed for object detection")
                return true
            }
            
            // Extract objects from image
            val result = objectDetectionExtractor.extractObjectsFromImage(mediaItem.uri)
            
            when (result) {
                is ObjectDetectionResult.Success -> {
                    // Check if media exists in database before inserting object detection results
                    val mediaExists = try {
                        database.mediaEntityDao().getMediaById(mediaItem.id) != null
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not verify media existence for ${mediaItem.id}, skipping object detection save", e)
                        false
                    }

                    if (!mediaExists) {
                        Log.w(TAG, "Media ${mediaItem.id} not found in database, skipping object detection save")
                        return true // Still consider it processed to avoid reprocessing
                    }

                    // Save results to database
                    val entities = result.detectedObjects.map { detectedObject ->
                        ObjectDetectionEntity(
                            mediaId = mediaItem.id,
                            objectLabel = detectedObject.label,
                            confidenceScore = detectedObject.confidence,
                            detectionTimestamp = System.currentTimeMillis() / 1000,
                            boundingBoxLeft = detectedObject.boundingBox?.left,
                            boundingBoxTop = detectedObject.boundingBox?.top,
                            boundingBoxRight = detectedObject.boundingBox?.right,
                            boundingBoxBottom = detectedObject.boundingBox?.bottom,
                            processingTimeMs = result.processingTimeMs
                        )
                    }

                    try {
                        database.objectDetectionDao().insertObjectDetections(entities)
                        Log.d(TAG, "Successfully processed image ${mediaItem.id}: ${entities.size} objects detected")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to save object detection results for image ${mediaItem.id}", e)
                        return false
                    }

                    true
                }
                is ObjectDetectionResult.Error -> {
                    Log.e(TAG, "Failed to process image ${mediaItem.id}: ${result.message}")
                    false
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Exception processing image ${mediaItem.id}", e)
            false
        }
    }
    
    /**
     * Start batch processing of unprocessed images
     */
    fun processBatch(batchSize: Int = 10) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED) // Object detection works offline
            .setRequiresBatteryNotLow(true)
            .setRequiresDeviceIdle(false)
            .build()
        
        val workData = Data.Builder()
            .putInt("batch_size", batchSize)
            .build()
        
        val workRequest = OneTimeWorkRequestBuilder<ObjectDetectionWorker>()
            .setConstraints(constraints)
            .setInputData(workData)
            .setInitialDelay(1, TimeUnit.SECONDS)
            .build()
        
        workManager.enqueueUniqueWork(
            WORK_NAME_BATCH_OBJECT_DETECTION,
            ExistingWorkPolicy.REPLACE,
            workRequest
        )
        
        Log.d(TAG, "Enqueued batch object detection work")
    }
    
    /**
     * Process a single image in background
     */
    fun processImageInBackground(mediaItem: MediaStoreData) {
        val workData = Data.Builder()
            .putLong("media_id", mediaItem.id)
            .putString("media_uri", mediaItem.uri.toString())
            .build()
        
        val workRequest = OneTimeWorkRequestBuilder<ObjectDetectionWorker>()
            .setInputData(workData)
            .build()
        
        workManager.enqueueUniqueWork(
            WORK_NAME_SINGLE_OBJECT_DETECTION + mediaItem.id,
            ExistingWorkPolicy.KEEP,
            workRequest
        )
        
        Log.d(TAG, "Enqueued single image object detection work for: ${mediaItem.id}")
    }
    
    /**
     * Search for media by object labels
     */
    suspend fun searchByObjectLabels(
        objectLabels: List<String>, 
        minConfidence: Float = 0.5f
    ): List<Long> {
        return try {
            database.objectDetectionDao().searchMediaByObjectLabels(objectLabels, minConfidence)
                .map { it.media_id }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to search by object labels", e)
            emptyList()
        }
    }
    
    /**
     * Get all detected object labels
     */
    suspend fun getAllDetectedObjectLabels(minConfidence: Float = 0.5f): List<String> {
        return try {
            database.objectDetectionDao().getAllDetectedObjectLabels(minConfidence)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get detected object labels", e)
            emptyList()
        }
    }
    
    /**
     * Get object detection statistics
     */
    suspend fun getObjectDetectionStats(): ObjectDetectionStats {
        return try {
            val stats = database.objectDetectionDao().getObjectDetectionStats()
            ObjectDetectionStats(
                totalProcessedImages = stats?.total_processed_images ?: 0,
                totalObjectsDetected = stats?.total_objects_detected ?: 0,
                averageConfidence = stats?.average_confidence ?: 0f,
                uniqueObjectTypes = stats?.unique_object_types ?: 0
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get object detection stats", e)
            ObjectDetectionStats(0, 0, 0f, 0)
        }
    }
    
    /**
     * Check if image has been processed
     */
    suspend fun isImageProcessed(mediaId: Long): Boolean {
        return try {
            database.objectDetectionDao().isMediaProcessed(mediaId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check if image is processed", e)
            false
        }
    }
    
    /**
     * Get objects detected in a specific image
     */
    suspend fun getObjectsForImage(mediaId: Long): List<ObjectDetectionEntity> {
        return try {
            database.objectDetectionDao().getObjectDetectionsByMediaId(mediaId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get objects for image $mediaId", e)
            emptyList()
        }
    }

    /**
     * Get total number of processed images
     */
    suspend fun getTotalProcessedImages(): Int {
        return try {
            database.objectDetectionDao().getTotalProcessedImages()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get total processed images count", e)
            0
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        objectDetectionExtractor.cleanup()
    }
}

/**
 * Data class for object detection statistics
 */
data class ObjectDetectionStats(
    val totalProcessedImages: Int,
    val totalObjectsDetected: Int,
    val averageConfidence: Float,
    val uniqueObjectTypes: Int
)
